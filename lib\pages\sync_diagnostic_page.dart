import 'package:flutter/material.dart';
import '../services/sync_verification_service.dart';
import '../services/unified_sync_service.dart';

/// Page de diagnostic pour vérifier la synchronisation de vos données
class SyncDiagnosticPage extends StatefulWidget {
  const SyncDiagnosticPage({super.key});

  @override
  State<SyncDiagnosticPage> createState() => _SyncDiagnosticPageState();
}

class _SyncDiagnosticPageState extends State<SyncDiagnosticPage> {
  SyncVerificationReport? _report;
  bool _isLoading = false;
  bool _isSyncing = false;

  @override
  void initState() {
    super.initState();
    _runDiagnostic();
  }

  Future<void> _runDiagnostic() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final verificationService = SyncVerificationService();
      final report = await verificationService.verifyDataIntegrity();

      setState(() {
        _report = report;
        _isLoading = false;
      });

      // Afficher le rapport détaillé dans la console
      verificationService.printDetailedReport(report);
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du diagnostic: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _forceMigration() async {
    setState(() {
      _isSyncing = true;
    });

    try {
      final unifiedSync = UnifiedSyncService();
      await unifiedSync.forceMigration();

      // Relancer le diagnostic
      await _runDiagnostic();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Migration forcée terminée avec succès!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la migration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSyncing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Diagnostic de Synchronisation'),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _runDiagnostic,
            tooltip: 'Actualiser le diagnostic',
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Vérification de vos données...'),
                  ],
                ),
              )
              : _report == null
              ? const Center(child: Text('Aucun rapport disponible'))
              : _buildReportView(),
    );
  }

  Widget _buildReportView() {
    final report = _report!;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Statut global
          _buildStatusCard(report),

          const SizedBox(height: 16),

          // Données locales (mobile)
          _buildDataCard(
            title: '📱 Données Mobiles (Locales)',
            subtitle: 'Vos données existantes sur mobile',
            data: {
              'Factures': report.localInvoicesCount,
              'Produits': report.localProductsCount,
              'Colis': report.localColisCount,
              'Tâches': report.localTasksCount,
              'Catégories': report.localCategoriesCount,
            },
            color: Colors.blue,
          ),

          const SizedBox(height: 16),

          // Données Supabase (web)
          _buildDataCard(
            title: '☁️ Données Web (Supabase)',
            subtitle: 'Données synchronisées pour le web',
            data: {
              'Factures': report.supabaseInvoicesCount,
              'Produits': report.supabaseProductsCount,
              'Colis': report.supabaseColisCount,
              'Tâches': report.supabaseTasksCount,
              'Catégories': report.supabaseCategoriesCount,
            },
            color: Colors.green,
          ),

          const SizedBox(height: 16),

          // Données Firebase
          _buildDataCard(
            title: '🔥 Données Firebase',
            subtitle: 'Ancien système de synchronisation',
            data: {
              'Factures': report.firebaseInvoicesCount,
              'Tâches': report.firebaseTasksCount,
              'Colis': report.firebaseColisCount,
            },
            color: Colors.orange,
          ),

          const SizedBox(height: 16),

          // Avertissements et erreurs
          if (report.warnings.isNotEmpty) ...[
            _buildMessagesCard(
              title: '⚠️ Avertissements',
              messages: report.warnings,
              color: Colors.orange,
            ),
            const SizedBox(height: 16),
          ],

          if (report.errors.isNotEmpty) ...[
            _buildMessagesCard(
              title: '❌ Erreurs',
              messages: report.errors,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
          ],

          // Actions
          _buildActionsCard(),
        ],
      ),
    );
  }

  Widget _buildStatusCard(SyncVerificationReport report) {
    Color statusColor;
    IconData statusIcon;

    switch (report.status) {
      case SyncStatus.success:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case SyncStatus.warning:
        statusColor = Colors.orange;
        statusIcon = Icons.warning;
        break;
      case SyncStatus.critical:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        break;
      case SyncStatus.error:
        statusColor = Colors.red;
        statusIcon = Icons.error_outline;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Icon(statusIcon, color: statusColor, size: 32),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Statut de Synchronisation',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      Text(
                        report.status.displayName,
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  '${report.syncPercentage.toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: statusColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: report.syncPercentage / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(statusColor),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataCard({
    required String title,
    required String subtitle,
    required Map<String, int> data,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 4,
                  height: 40,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        subtitle,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...data.entries.map(
              (entry) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(entry.key),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        entry.value.toString(),
                        style: TextStyle(
                          color: color,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessagesCard({
    required String title,
    required List<String> messages,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...messages.map(
              (message) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(Icons.circle, size: 8, color: color),
                    const SizedBox(width: 8),
                    Expanded(child: Text(message)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '🛠️ Actions',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isSyncing ? null : _forceMigration,
                icon:
                    _isSyncing
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.sync),
                label: Text(
                  _isSyncing ? 'Migration en cours...' : 'Forcer la Migration',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _isLoading ? null : _runDiagnostic,
                icon: const Icon(Icons.refresh),
                label: const Text('Actualiser le Diagnostic'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
