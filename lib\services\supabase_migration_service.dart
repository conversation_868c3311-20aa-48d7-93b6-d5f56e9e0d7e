import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/invoice.dart';
import '../models/colis.dart';
import '../models/product.dart';
import '../models/task.dart';
import '../models/category.dart';
import 'supabase_service.dart';
import 'logging_service.dart';

/// Service de migration des données vers Supabase
class SupabaseMigrationService {
  /// Tester la connexion Supabase
  static Future<bool> testConnection() async {
    try {
      await SupabaseService.testConnection();
      return true;
    } catch (e) {
      LoggingService.error('Erreur de connexion Supabase', 'MIGRATION', e);
      return false;
    }
  }

  /// Migrer les factures de SharedPreferences vers Supabase
  static Future<void> migrateInvoices() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final invoicesJson = prefs.getStringList('invoices') ?? [];

      for (final invoiceJson in invoicesJson) {
        final invoice = Invoice.fromJson(jsonDecode(invoiceJson));
        await SupabaseService.addInvoice(invoice);
      }

      LoggingService.migration(
        'Migration des factures terminée: ${invoicesJson.length} factures migrées',
      );
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la migration des factures',
        'MIGRATION',
        e,
      );
      rethrow;
    }
  }

  /// Migrer les colis de SharedPreferences vers Supabase
  static Future<void> migrateColis() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final colisJson = prefs.getStringList('colis') ?? [];

      for (final colisJsonString in colisJson) {
        final colis = Colis.fromJson(jsonDecode(colisJsonString));
        await SupabaseService.addColis(colis);
      }

      LoggingService.migration('Migration des colis terminée: ${colisJson.length} colis migrés');
    } catch (e) {
      LoggingService.error('Erreur lors de la migration des colis', 'MIGRATION', e);
      rethrow;
    }
  }

  /// Migrer les produits de SharedPreferences vers Supabase
  static Future<void> migrateProducts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final productsJson = prefs.getStringList('products') ?? [];

      for (final productJson in productsJson) {
        final product = Product.fromJson(jsonDecode(productJson));
        await SupabaseService.addProduct(product);
      }

      print(
        'Migration des produits terminée: ${productsJson.length} produits migrés',
      );
    } catch (e) {
      LoggingService.error('Erreur lors de la migration des produits', 'MIGRATION', e);
      rethrow;
    }
  }

  /// Migrer les tâches de SharedPreferences vers Supabase
  static Future<void> migrateTasks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tasksJson = prefs.getStringList('tasks') ?? [];

      for (final taskJson in tasksJson) {
        final task = Task.fromJson(jsonDecode(taskJson));
        await SupabaseService.addTask(task);
      }

      print(
        'Migration des tâches terminée: ${tasksJson.length} tâches migrées',
      );
    } catch (e) {
      LoggingService.error('Erreur lors de la migration des tâches', 'MIGRATION', e);
      rethrow;
    }
  }

  /// Migrer les catégories de SharedPreferences vers Supabase
  static Future<void> migrateCategories() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = prefs.getStringList('categories') ?? [];

      for (final categoryJson in categoriesJson) {
        final category = Category.fromJson(jsonDecode(categoryJson));
        await SupabaseService.addCategory(category);
      }

      print(
        'Migration des catégories terminée: ${categoriesJson.length} catégories migrées',
      );
    } catch (e) {
      LoggingService.error('Erreur lors de la migration des catégories', 'MIGRATION', e);
      rethrow;
    }
  }

  /// Migrer toutes les données vers Supabase
  static Future<void> migrateAll() async {
    try {
      LoggingService.migration('Début de la migration vers Supabase...');

      // Tester la connexion
      final isConnected = await testConnection();
      if (!isConnected) {
        throw Exception('Impossible de se connecter à Supabase');
      }

      // Migrer toutes les données
      await migrateInvoices();
      await migrateColis();
      await migrateProducts();
      await migrateTasks();
      await migrateCategories();

      LoggingService.success('Migration complète vers Supabase terminée avec succès!', 'MIGRATION');
    } catch (e) {
      LoggingService.error('Erreur lors de la migration', 'MIGRATION', e);
      rethrow;
    }
  }
}
