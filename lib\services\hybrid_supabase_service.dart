import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/invoice.dart';
import '../models/colis.dart';
import '../models/product.dart';
import '../models/task.dart';
import '../models/category.dart';
import 'supabase_service.dart';
import 'supabase_migration_service.dart';
import 'logging_service.dart';

/// Service hybride pour les données (Supabase + SharedPreferences)
class HybridSupabaseService {
  static bool _useSupabase = true;

  /// Méthode utilitaire pour gérer les fallbacks
  static void _handleSupabaseError(String operation, dynamic error) {
    LoggingService.warning(
      'Erreur Supabase pour $operation, fallback vers SharedPreferences',
      'HYBRID',
    );
    LoggingService.error('Détails de l\'erreur', 'HYBRID', error);
    _useSupabase = false;
  }

  /// Initialiser le service hybride
  static Future<void> initialize() async {
    try {
      LoggingService.info('Initialisation du service hybride', 'HYBRID');
      // Tester la connexion Supabase
      final isConnected = await SupabaseMigrationService.testConnection();
      _useSupabase = isConnected;

      if (_useSupabase) {
        LoggingService.success(
          'Supabase connecté, utilisation du cloud',
          'HYBRID',
        );
        // Migrer les données si nécessaire
        await SupabaseMigrationService.migrateAll();
      } else {
        LoggingService.warning(
          'Supabase non disponible, utilisation de SharedPreferences',
          'HYBRID',
        );
      }
    } catch (e) {
      _useSupabase = false;
      LoggingService.error(
        'Supabase non disponible, utilisation de SharedPreferences',
        'HYBRID',
        e,
      );
    }
  }

  // ===== FACTURES =====

  /// Récupérer toutes les factures
  static Future<List<Invoice>> getInvoices() async {
    if (_useSupabase) {
      try {
        return await SupabaseService.getInvoices();
      } catch (e) {
        _handleSupabaseError('getInvoices', e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final invoicesJson = prefs.getStringList('invoices') ?? [];
    return invoicesJson
        .map((json) => Invoice.fromJson(jsonDecode(json)))
        .toList();
  }

  /// Ajouter une nouvelle facture
  static Future<void> addInvoice(Invoice invoice) async {
    if (_useSupabase) {
      try {
        await SupabaseService.addInvoice(invoice);
      } catch (e) {
        _handleSupabaseError('addInvoice', e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final invoicesJson = prefs.getStringList('invoices') ?? [];
    invoicesJson.add(jsonEncode(invoice.toJson()));
    await prefs.setStringList('invoices', invoicesJson);
  }

  /// Mettre à jour une facture
  static Future<void> updateInvoice(String id, Invoice invoice) async {
    if (_useSupabase) {
      try {
        await SupabaseService.updateInvoice(id, invoice);
      } catch (e) {
        _handleSupabaseError('updateInvoice', e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final invoicesJson = prefs.getStringList('invoices') ?? [];
    final index = invoicesJson.indexWhere((json) {
      final invoiceData = jsonDecode(json);
      return invoiceData['id'] == id;
    });

    if (index != -1) {
      invoicesJson[index] = jsonEncode(invoice.toJson());
      await prefs.setStringList('invoices', invoicesJson);
    }
  }

  /// Supprimer une facture
  static Future<void> deleteInvoice(String id) async {
    if (_useSupabase) {
      try {
        await SupabaseService.deleteInvoice(id);
      } catch (e) {
        _handleSupabaseError('deleteInvoice', e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final invoicesJson = prefs.getStringList('invoices') ?? [];
    invoicesJson.removeWhere((json) {
      final invoiceData = jsonDecode(json);
      return invoiceData['id'] == id;
    });
    await prefs.setStringList('invoices', invoicesJson);
  }

  // ===== COLIS =====

  /// Récupérer tous les colis
  static Future<List<Colis>> getColis() async {
    if (_useSupabase) {
      try {
        return await SupabaseService.getColis();
      } catch (e) {
        _handleSupabaseError('getColis', e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final colisJson = prefs.getStringList('colis') ?? [];
    return colisJson.map((json) => Colis.fromJson(jsonDecode(json))).toList();
  }

  /// Ajouter un nouveau colis
  static Future<void> addColis(Colis colis) async {
    if (_useSupabase) {
      try {
        await SupabaseService.addColis(colis);
      } catch (e) {
        _handleSupabaseError('addColis', e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final colisJson = prefs.getStringList('colis') ?? [];
    colisJson.add(jsonEncode(colis.toJson()));
    await prefs.setStringList('colis', colisJson);
  }

  /// Mettre à jour un colis
  static Future<void> updateColis(String id, Colis colis) async {
    if (_useSupabase) {
      try {
        await SupabaseService.updateColis(id, colis);
      } catch (e) {
        _handleSupabaseError('updateColis', e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final colisJson = prefs.getStringList('colis') ?? [];
    final index = colisJson.indexWhere((json) {
      final colisData = jsonDecode(json);
      return colisData['id'] == id;
    });

    if (index != -1) {
      colisJson[index] = jsonEncode(colis.toJson());
      await prefs.setStringList('colis', colisJson);
    }
  }

  /// Supprimer un colis
  static Future<void> deleteColis(String id) async {
    if (_useSupabase) {
      try {
        await SupabaseService.deleteColis(id);
      } catch (e) {
        _handleSupabaseError("deleteColis", e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final colisJson = prefs.getStringList('colis') ?? [];
    colisJson.removeWhere((json) {
      final colisData = jsonDecode(json);
      return colisData['id'] == id;
    });
    await prefs.setStringList('colis', colisJson);
  }

  // ===== PRODUITS =====

  /// Récupérer tous les produits
  static Future<List<Product>> getProducts() async {
    if (_useSupabase) {
      try {
        return await SupabaseService.getProducts();
      } catch (e) {
        _handleSupabaseError("getProducts", e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final productsJson = prefs.getStringList('products') ?? [];
    return productsJson
        .map((json) => Product.fromJson(jsonDecode(json)))
        .toList();
  }

  /// Ajouter un nouveau produit
  static Future<void> addProduct(Product product) async {
    if (_useSupabase) {
      try {
        await SupabaseService.addProduct(product);
      } catch (e) {
        _handleSupabaseError("addProduct", e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final productsJson = prefs.getStringList('products') ?? [];
    productsJson.add(jsonEncode(product.toJson()));
    await prefs.setStringList('products', productsJson);
  }

  /// Mettre à jour un produit
  static Future<void> updateProduct(String id, Product product) async {
    if (_useSupabase) {
      try {
        await SupabaseService.updateProduct(id, product);
      } catch (e) {
        _handleSupabaseError("updateProduct", e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final productsJson = prefs.getStringList('products') ?? [];
    final index = productsJson.indexWhere((json) {
      final productData = jsonDecode(json);
      return productData['id'] == id;
    });

    if (index != -1) {
      productsJson[index] = jsonEncode(product.toJson());
      await prefs.setStringList('products', productsJson);
    }
  }

  /// Supprimer un produit
  static Future<void> deleteProduct(String id) async {
    if (_useSupabase) {
      try {
        await SupabaseService.deleteProduct(id);
      } catch (e) {
        _handleSupabaseError("deleteProduct", e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final productsJson = prefs.getStringList('products') ?? [];
    productsJson.removeWhere((json) {
      final productData = jsonDecode(json);
      return productData['id'] == id;
    });
    await prefs.setStringList('products', productsJson);
  }

  // ===== TÂCHES =====

  /// Récupérer toutes les tâches
  static Future<List<Task>> getTasks() async {
    if (_useSupabase) {
      try {
        return await SupabaseService.getTasks();
      } catch (e) {
        _handleSupabaseError("getTasks", e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final tasksJson = prefs.getStringList('tasks') ?? [];
    return tasksJson.map((json) => Task.fromJson(jsonDecode(json))).toList();
  }

  /// Ajouter une nouvelle tâche
  static Future<void> addTask(Task task) async {
    if (_useSupabase) {
      try {
        await SupabaseService.addTask(task);
      } catch (e) {
        _handleSupabaseError("addTask", e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final tasksJson = prefs.getStringList('tasks') ?? [];
    tasksJson.add(jsonEncode(task.toJson()));
    await prefs.setStringList('tasks', tasksJson);
  }

  /// Mettre à jour une tâche
  static Future<void> updateTask(String id, Task task) async {
    if (_useSupabase) {
      try {
        await SupabaseService.updateTask(id, task);
      } catch (e) {
        _handleSupabaseError("updateTask", e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final tasksJson = prefs.getStringList('tasks') ?? [];
    final index = tasksJson.indexWhere((json) {
      final taskData = jsonDecode(json);
      return taskData['id'] == id;
    });

    if (index != -1) {
      tasksJson[index] = jsonEncode(task.toJson());
      await prefs.setStringList('tasks', tasksJson);
    }
  }

  /// Supprimer une tâche
  static Future<void> deleteTask(String id) async {
    if (_useSupabase) {
      try {
        await SupabaseService.deleteTask(id);
      } catch (e) {
        _handleSupabaseError("deleteTask", e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final tasksJson = prefs.getStringList('tasks') ?? [];
    tasksJson.removeWhere((json) {
      final taskData = jsonDecode(json);
      return taskData['id'] == id;
    });
    await prefs.setStringList('tasks', tasksJson);
  }

  // ===== CATÉGORIES =====

  /// Récupérer toutes les catégories
  static Future<List<Category>> getCategories() async {
    if (_useSupabase) {
      try {
        return await SupabaseService.getCategories();
      } catch (e) {
        _handleSupabaseError("getCategories", e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = prefs.getStringList('categories') ?? [];
    return categoriesJson
        .map((json) => Category.fromJson(jsonDecode(json)))
        .toList();
  }

  /// Ajouter une nouvelle catégorie
  static Future<void> addCategory(Category category) async {
    if (_useSupabase) {
      try {
        await SupabaseService.addCategory(category);
      } catch (e) {
        _handleSupabaseError("addCategory", e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = prefs.getStringList('categories') ?? [];
    categoriesJson.add(jsonEncode(category.toJson()));
    await prefs.setStringList('categories', categoriesJson);
  }

  /// Mettre à jour une catégorie
  static Future<void> updateCategory(String id, Category category) async {
    if (_useSupabase) {
      try {
        await SupabaseService.updateCategory(id, category);
      } catch (e) {
        _handleSupabaseError("updateCategory", e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = prefs.getStringList('categories') ?? [];
    final index = categoriesJson.indexWhere((json) {
      final categoryData = jsonDecode(json);
      return categoryData['id'] == id;
    });

    if (index != -1) {
      categoriesJson[index] = jsonEncode(category.toJson());
      await prefs.setStringList('categories', categoriesJson);
    }
  }

  /// Supprimer une catégorie
  static Future<void> deleteCategory(String id) async {
    if (_useSupabase) {
      try {
        await SupabaseService.deleteCategory(id);
      } catch (e) {
        _handleSupabaseError("deleteCategory", e);
      }
    }

    // Fallback vers SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = prefs.getStringList('categories') ?? [];
    categoriesJson.removeWhere((json) {
      final categoryData = jsonDecode(json);
      return categoryData['id'] == id;
    });
    await prefs.setStringList('categories', categoriesJson);
  }

  // ===== UTILITAIRES =====

  /// Vérifier si Supabase est utilisé
  static bool get isUsingSupabase => _useSupabase;

  /// Forcer l'utilisation de Supabase
  static void forceSupabase() {
    _useSupabase = true;
  }

  /// Forcer l'utilisation de SharedPreferences
  static void forceLocal() {
    _useSupabase = false;
  }
}
