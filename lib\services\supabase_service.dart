import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/invoice.dart';
import '../models/colis.dart';
import '../models/product.dart';
import '../models/task.dart';
import '../models/category.dart';
import 'supabase_config.dart';
import 'logging_service.dart';

/// Service principal pour gérer toutes les opérations Supabase
class SupabaseService {
  static final SupabaseClient _client = Supabase.instance.client;

  // ===== FACTURES =====

  /// Récupérer toutes les factures
  static Future<List<Invoice>> getInvoices() async {
    try {
      LoggingService.database('SELECT', SupabaseConfig.invoicesTable);
      final response = await _client
          .from(SupabaseConfig.invoicesTable)
          .select()
          .order('created_at', ascending: false);

      final invoices =
          (response as List).map((json) => Invoice.fromJson(json)).toList();
      LoggingService.database(
        'SELECT',
        SupabaseConfig.invoicesTable,
        '${invoices.length} factures récupérées',
      );
      return invoices;
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la récupération des factures',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  /// Ajouter une nouvelle facture
  static Future<void> addInvoice(Invoice invoice) async {
    try {
      LoggingService.database(
        'INSERT',
        SupabaseConfig.invoicesTable,
        'ID: ${invoice.id}',
      );
      await _client.from(SupabaseConfig.invoicesTable).insert(invoice.toJson());
      LoggingService.success('Facture ajoutée avec succès', 'SUPABASE');
    } catch (e) {
      LoggingService.error(
        'Erreur lors de l\'ajout de la facture',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  /// Mettre à jour une facture
  static Future<void> updateInvoice(String id, Invoice invoice) async {
    try {
      LoggingService.database(
        'UPDATE',
        SupabaseConfig.invoicesTable,
        'ID: $id',
      );
      await _client
          .from(SupabaseConfig.invoicesTable)
          .update(invoice.toJson())
          .eq('id', id);
      LoggingService.success('Facture mise à jour avec succès', 'SUPABASE');
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la mise à jour de la facture',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  /// Supprimer une facture
  static Future<void> deleteInvoice(String id) async {
    try {
      LoggingService.database(
        'DELETE',
        SupabaseConfig.invoicesTable,
        'ID: $id',
      );
      await _client.from(SupabaseConfig.invoicesTable).delete().eq('id', id);
      LoggingService.success('Facture supprimée avec succès', 'SUPABASE');
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la suppression de la facture',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  // ===== COLIS =====

  /// Récupérer tous les colis
  static Future<List<Colis>> getColis() async {
    try {
      LoggingService.database('SELECT', SupabaseConfig.colisTable);
      final response = await _client
          .from(SupabaseConfig.colisTable)
          .select()
          .order('created_at', ascending: false);

      final colis =
          (response as List).map((json) => Colis.fromJson(json)).toList();
      LoggingService.database(
        'SELECT',
        SupabaseConfig.colisTable,
        '${colis.length} colis récupérés',
      );
      return colis;
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la récupération des colis',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  /// Ajouter un nouveau colis
  static Future<void> addColis(Colis colis) async {
    try {
      LoggingService.database(
        'INSERT',
        SupabaseConfig.colisTable,
        'ID: ${colis.id}',
      );
      await _client.from(SupabaseConfig.colisTable).insert(colis.toJson());
      LoggingService.success('Colis ajouté avec succès', 'SUPABASE');
    } catch (e) {
      LoggingService.error('Erreur lors de l\'ajout du colis', 'SUPABASE', e);
      rethrow;
    }
  }

  /// Mettre à jour un colis
  static Future<void> updateColis(String id, Colis colis) async {
    try {
      LoggingService.database('UPDATE', SupabaseConfig.colisTable, 'ID: $id');
      await _client
          .from(SupabaseConfig.colisTable)
          .update(colis.toJson())
          .eq('id', id);
      LoggingService.success('Colis mis à jour avec succès', 'SUPABASE');
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la mise à jour du colis',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  /// Supprimer un colis
  static Future<void> deleteColis(String id) async {
    try {
      LoggingService.database('DELETE', SupabaseConfig.colisTable, 'ID: $id');
      await _client.from(SupabaseConfig.colisTable).delete().eq('id', id);
      LoggingService.success('Colis supprimé avec succès', 'SUPABASE');
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la suppression du colis',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  // ===== PRODUITS =====

  /// Récupérer tous les produits
  static Future<List<Product>> getProducts() async {
    try {
      LoggingService.database('SELECT', SupabaseConfig.productsTable);
      final response = await _client
          .from(SupabaseConfig.productsTable)
          .select()
          .order('name', ascending: true);

      final products =
          (response as List).map((json) => Product.fromJson(json)).toList();
      LoggingService.database(
        'SELECT',
        SupabaseConfig.productsTable,
        '${products.length} produits récupérés',
      );
      return products;
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la récupération des produits',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  /// Ajouter un nouveau produit
  static Future<void> addProduct(Product product) async {
    try {
      LoggingService.database(
        'INSERT',
        SupabaseConfig.productsTable,
        'ID: ${product.id}',
      );
      await _client.from(SupabaseConfig.productsTable).insert(product.toJson());
      LoggingService.success('Produit ajouté avec succès', 'SUPABASE');
    } catch (e) {
      LoggingService.error('Erreur lors de l\'ajout du produit', 'SUPABASE', e);
      rethrow;
    }
  }

  /// Mettre à jour un produit
  static Future<void> updateProduct(String id, Product product) async {
    try {
      LoggingService.database(
        'UPDATE',
        SupabaseConfig.productsTable,
        'ID: $id',
      );
      await _client
          .from(SupabaseConfig.productsTable)
          .update(product.toJson())
          .eq('id', id);
      LoggingService.success('Produit mis à jour avec succès', 'SUPABASE');
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la mise à jour du produit',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  /// Supprimer un produit
  static Future<void> deleteProduct(String id) async {
    try {
      LoggingService.database(
        'DELETE',
        SupabaseConfig.productsTable,
        'ID: $id',
      );
      await _client.from(SupabaseConfig.productsTable).delete().eq('id', id);
      LoggingService.success('Produit supprimé avec succès', 'SUPABASE');
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la suppression du produit',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  // ===== TÂCHES =====

  /// Récupérer toutes les tâches
  static Future<List<Task>> getTasks() async {
    try {
      LoggingService.database('SELECT', SupabaseConfig.tasksTable);
      final response = await _client
          .from(SupabaseConfig.tasksTable)
          .select()
          .order('created_at', ascending: false);

      final tasks =
          (response as List).map((json) => Task.fromJson(json)).toList();
      LoggingService.database(
        'SELECT',
        SupabaseConfig.tasksTable,
        '${tasks.length} tâches récupérées',
      );
      return tasks;
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la récupération des tâches',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  /// Ajouter une nouvelle tâche
  static Future<void> addTask(Task task) async {
    try {
      LoggingService.database(
        'INSERT',
        SupabaseConfig.tasksTable,
        'ID: ${task.id}',
      );
      await _client.from(SupabaseConfig.tasksTable).insert(task.toJson());
      LoggingService.success('Tâche ajoutée avec succès', 'SUPABASE');
    } catch (e) {
      LoggingService.error(
        'Erreur lors de l\'ajout de la tâche',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  /// Mettre à jour une tâche
  static Future<void> updateTask(String id, Task task) async {
    try {
      LoggingService.database('UPDATE', SupabaseConfig.tasksTable, 'ID: $id');
      await _client
          .from(SupabaseConfig.tasksTable)
          .update(task.toJson())
          .eq('id', id);
      LoggingService.success('Tâche mise à jour avec succès', 'SUPABASE');
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la mise à jour de la tâche',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  /// Supprimer une tâche
  static Future<void> deleteTask(String id) async {
    try {
      LoggingService.database('DELETE', SupabaseConfig.tasksTable, 'ID: $id');
      await _client.from(SupabaseConfig.tasksTable).delete().eq('id', id);
      LoggingService.success('Tâche supprimée avec succès', 'SUPABASE');
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la suppression de la tâche',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  // ===== CATÉGORIES =====

  /// Récupérer toutes les catégories
  static Future<List<Category>> getCategories() async {
    try {
      LoggingService.database('SELECT', SupabaseConfig.categoriesTable);
      final response = await _client
          .from(SupabaseConfig.categoriesTable)
          .select()
          .order('name', ascending: true);

      final categories =
          (response as List).map((json) => Category.fromJson(json)).toList();
      LoggingService.database(
        'SELECT',
        SupabaseConfig.categoriesTable,
        '${categories.length} catégories récupérées',
      );
      return categories;
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la récupération des catégories',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  /// Ajouter une nouvelle catégorie
  static Future<void> addCategory(Category category) async {
    try {
      LoggingService.database(
        'INSERT',
        SupabaseConfig.categoriesTable,
        'ID: ${category.id}',
      );
      await _client
          .from(SupabaseConfig.categoriesTable)
          .insert(category.toJson());
      LoggingService.success('Catégorie ajoutée avec succès', 'SUPABASE');
    } catch (e) {
      LoggingService.error(
        'Erreur lors de l\'ajout de la catégorie',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  /// Mettre à jour une catégorie
  static Future<void> updateCategory(String id, Category category) async {
    try {
      LoggingService.database(
        'UPDATE',
        SupabaseConfig.categoriesTable,
        'ID: $id',
      );
      await _client
          .from(SupabaseConfig.categoriesTable)
          .update(category.toJson())
          .eq('id', id);
      LoggingService.success('Catégorie mise à jour avec succès', 'SUPABASE');
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la mise à jour de la catégorie',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  /// Supprimer une catégorie
  static Future<void> deleteCategory(String id) async {
    try {
      LoggingService.database(
        'DELETE',
        SupabaseConfig.categoriesTable,
        'ID: $id',
      );
      await _client.from(SupabaseConfig.categoriesTable).delete().eq('id', id);
      LoggingService.success('Catégorie supprimée avec succès', 'SUPABASE');
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la suppression de la catégorie',
        'SUPABASE',
        e,
      );
      rethrow;
    }
  }

  // ===== STOCKAGE DE FICHIERS =====

  /// Uploader une image
  static Future<String> uploadImage(String filePath, String fileName) async {
    try {
      LoggingService.info('Upload image: $fileName', 'STORAGE');
      final file = File(filePath);
      final response = await _client.storage
          .from(SupabaseConfig.storageBucket)
          .upload('images/$fileName', file);

      LoggingService.success(
        'Image uploadée avec succès: $fileName',
        'STORAGE',
      );
      return response;
    } catch (e) {
      LoggingService.error(
        'Erreur lors de l\'upload de l\'image',
        'STORAGE',
        e,
      );
      rethrow;
    }
  }

  /// Télécharger une image
  static Future<String> downloadImage(String fileName) async {
    try {
      LoggingService.info('Download image: $fileName', 'STORAGE');
      final response = await _client.storage
          .from(SupabaseConfig.storageBucket)
          .createSignedUrl('images/$fileName', 3600); // 1 heure

      LoggingService.success(
        'URL image générée avec succès: $fileName',
        'STORAGE',
      );
      return response;
    } catch (e) {
      LoggingService.error(
        'Erreur lors du téléchargement de l\'image',
        'STORAGE',
        e,
      );
      rethrow;
    }
  }

  /// Uploader un document
  static Future<String> uploadDocument(String filePath, String fileName) async {
    try {
      LoggingService.info('Upload document: $fileName', 'STORAGE');
      final file = File(filePath);
      final response = await _client.storage
          .from(SupabaseConfig.storageBucket)
          .upload('documents/$fileName', file);

      LoggingService.success(
        'Document uploadé avec succès: $fileName',
        'STORAGE',
      );
      return response;
    } catch (e) {
      LoggingService.error(
        'Erreur lors de l\'upload du document',
        'STORAGE',
        e,
      );
      rethrow;
    }
  }

  /// Télécharger un document
  static Future<String> downloadDocument(String fileName) async {
    try {
      LoggingService.info('Download document: $fileName', 'STORAGE');
      final response = await _client.storage
          .from(SupabaseConfig.storageBucket)
          .createSignedUrl('documents/$fileName', 3600); // 1 heure

      LoggingService.success(
        'URL document générée avec succès: $fileName',
        'STORAGE',
      );
      return response;
    } catch (e) {
      LoggingService.error(
        'Erreur lors du téléchargement du document',
        'STORAGE',
        e,
      );
      rethrow;
    }
  }

  // ===== TESTS DE CONNEXION =====

  /// Tester la connexion Supabase
  static Future<bool> testConnection() async {
    try {
      LoggingService.network('Test de connexion Supabase');
      await _client.from(SupabaseConfig.invoicesTable).select('id').limit(1);
      LoggingService.success('Connexion Supabase réussie', 'NETWORK');
      return true;
    } catch (e) {
      LoggingService.error('Erreur de connexion Supabase', 'NETWORK', e);
      return false;
    }
  }
}
