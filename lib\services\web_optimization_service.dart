import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'hybrid_supabase_service.dart';
import 'logging_service.dart';
import 'platform_uniformity_service.dart';

/// Service d'optimisation pour la version web
class WebOptimizationService {
  static final SupabaseClient _client = Supabase.instance.client;

  /// Vérifier si l'application tourne sur le web
  static bool get isWeb => kIsWeb;

  /// Initialiser les optimisations web
  static Future<void> initializeWebOptimizations() async {
    if (!isWeb) return;

    LoggingService.info('Initialisation des optimisations web...', 'WEB');

    // Activer la synchronisation en temps réel pour le web
    await _enableRealtimeSync();

    // Optimiser les requêtes pour le web
    await _optimizeQueries();

    // Appliquer l'uniformité entre web et mobile
    await _applyUniformExperience();

    LoggingService.success('Optimisations web initialisées', 'WEB');
  }

  /// Applique l'uniformité entre web et mobile
  static Future<void> _applyUniformExperience() async {
    LoggingService.info('Application de l\'uniformité web-mobile...', 'WEB');

    // Utiliser les mêmes configurations que mobile

    // S'assurer que l'expérience web est identique au mobile
    if (PlatformUniformityService.shouldUseUniformNavigation()) {
      LoggingService.info('Navigation uniforme activée', 'WEB');
    }

    LoggingService.success('Uniformité web-mobile appliquée', 'WEB');
  }

  /// Activer la synchronisation en temps réel
  static Future<void> _enableRealtimeSync() async {
    try {
      // Écouter les changements de factures
      _client
          .channel('invoices_changes')
          .onPostgresChanges(
            event: PostgresChangeEvent.all,
            schema: 'public',
            table: 'invoices',
            callback: (payload) {
              LoggingService.info(
                'Changement facture détecté: ${payload.newRecord}',
                'REALTIME',
              );
              // Ici vous pouvez déclencher une mise à jour de l'interface
            },
          )
          .subscribe();

      // Écouter les changements de colis
      _client
          .channel('colis_changes')
          .onPostgresChanges(
            event: PostgresChangeEvent.all,
            schema: 'public',
            table: 'colis',
            callback: (payload) {
              LoggingService.info(
                'Changement colis détecté: ${payload.newRecord}',
                'REALTIME',
              );
              // Ici vous pouvez déclencher une mise à jour de l'interface
            },
          )
          .subscribe();

      // Écouter les changements de produits
      _client
          .channel('products_changes')
          .onPostgresChanges(
            event: PostgresChangeEvent.all,
            schema: 'public',
            table: 'products',
            callback: (payload) {
              LoggingService.info(
                'Changement produit détecté: ${payload.newRecord}',
                'REALTIME',
              );
              // Ici vous pouvez déclencher une mise à jour de l'interface
            },
          )
          .subscribe();
    } catch (e) {
      print(
        'Erreur lors de l\'activation de la synchronisation temps réel: $e',
      );
    }
  }

  /// Optimiser les requêtes pour le web
  static Future<void> _optimizeQueries() async {
    // Pour le web, on peut optimiser les requêtes avec des limites
    // et des paginations pour de meilleures performances
    LoggingService.info('Optimisation des requêtes web activée', 'WEB');
  }

  /// Obtenir les statistiques en temps réel pour le dashboard web
  static Future<Map<String, dynamic>> getRealtimeStats() async {
    try {
      // Requêtes optimisées pour le web
      final invoicesResponse = await _client
          .from('invoices')
          .select('id, total, status, created_at')
          .order('created_at', ascending: false)
          .limit(50);

      final colisResponse = await _client
          .from('colis')
          .select('id, statut, reste_a_payer, frais_livraison, date_ajout')
          .order('date_ajout', ascending: false)
          .limit(50);

      final productsResponse = await _client
          .from('products')
          .select('id, name, price, quantity')
          .order('name', ascending: true);

      // Calculer les statistiques
      final invoices = invoicesResponse as List;
      final colis = colisResponse as List;
      final products = productsResponse as List;

      double totalRevenue = 0;
      int paidInvoices = 0;
      int pendingInvoices = 0;

      for (final invoice in invoices) {
        totalRevenue += (invoice['total'] as num).toDouble();
        if (invoice['status'] == 'payee') {
          paidInvoices++;
        } else {
          pendingInvoices++;
        }
      }

      int completedDeliveries = 0;
      int pendingDeliveries = 0;
      double totalDeliveryRevenue = 0;

      for (final coli in colis) {
        if (coli['statut'] == 'livree') {
          completedDeliveries++;
        } else {
          pendingDeliveries++;
        }
        totalDeliveryRevenue += (coli['reste_a_payer'] as num).toDouble();
      }

      return {
        'totalInvoices': invoices.length,
        'totalRevenue': totalRevenue,
        'paidInvoices': paidInvoices,
        'pendingInvoices': pendingInvoices,
        'totalDeliveries': colis.length,
        'completedDeliveries': completedDeliveries,
        'pendingDeliveries': pendingDeliveries,
        'totalDeliveryRevenue': totalDeliveryRevenue,
        'totalProducts': products.length,
        'lastUpdate': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la récupération des statistiques',
        'WEB',
        e,
      );
      return {
        'totalInvoices': 0,
        'totalRevenue': 0,
        'paidInvoices': 0,
        'pendingInvoices': 0,
        'totalDeliveries': 0,
        'completedDeliveries': 0,
        'pendingDeliveries': 0,
        'totalDeliveryRevenue': 0,
        'totalProducts': 0,
        'lastUpdate': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Synchroniser les données locales avec Supabase
  static Future<void> syncLocalData() async {
    if (!isWeb) return;

    try {
      LoggingService.info('Synchronisation des données locales...', 'WEB');

      // Forcer l'utilisation de Supabase pour le web
      HybridSupabaseService.forceSupabase();

      // Migrer les données si nécessaire
      // Cette fonction sera appelée automatiquement par HybridSupabaseService

      LoggingService.success('Synchronisation terminée', 'WEB');
    } catch (e) {
      LoggingService.error('Erreur lors de la synchronisation', 'WEB', e);
    }
  }
}
